import React, { useState } from 'react';
import type { AdMeshRecommendation, AdMeshTheme } from '../types';
import { AdMeshLinkTracker } from './AdMeshLinkTracker';

export interface AdMeshExpandableUnitProps {
  /** Product recommendation data */
  recommendation: AdMeshRecommendation;
  /** Theme configuration */
  theme?: AdMeshTheme;
  /** Custom CSS class name */
  className?: string;
  /** Callback when the ad is clicked */
  onClick?: (adId: string, admeshLink: string) => void;
  /** Show "powered by AdMesh" branding */
  showPoweredBy?: boolean;
  /** Initial expanded state */
  initialExpanded?: boolean;
  /** Custom sections to display */
  sections?: {
    title: string;
    description: string;
    icon?: string;
  }[];
  /** Custom call-to-action text */
  ctaText?: string;
  /** Show collapse/expand functionality */
  collapsible?: boolean;
}

/**
 * AdMeshExpandableUnit - A comprehensive ad unit with expandable sections
 * 
 * Similar to the Temporal ad format, this component displays:
 * - Header with product name and sponsor info
 * - Multiple expandable sections with descriptions
 * - Primary call-to-action button
 * - Optional powered by branding
 */
export const AdMeshExpandableUnit: React.FC<AdMeshExpandableUnitProps> = ({
  recommendation,
  theme = { mode: 'light' },
  className = '',
  onClick,
  showPoweredBy = true,
  initialExpanded = false,
  sections,
  ctaText,
  collapsible = true
}) => {
  const [isExpanded, setIsExpanded] = useState(initialExpanded);

  const handleClick = () => {
    onClick?.(recommendation.ad_id, recommendation.admesh_link);
  };

  const handleToggleExpand = () => {
    if (collapsible) {
      setIsExpanded(!isExpanded);
    }
  };

  // Default sections based on recommendation data
  const defaultSections = [
    {
      title: 'Documentation',
      description: `Learn more about ${recommendation.title}. Start exploring the features and capabilities.`,
      icon: '📚'
    },
    {
      title: 'Talk To An Expert',
      description: `Ready to learn more about ${recommendation.title}? Reach out to a platform specialist for personalized guidance.`,
      icon: '💬'
    },
    {
      title: `${recommendation.title} Features`,
      description: recommendation.description || `${recommendation.title} offers comprehensive solutions for your needs. Discover the full potential.`,
      icon: '⚡'
    },
    {
      title: 'How it Works',
      description: `Learn how to get started with ${recommendation.title}. Begin your journey today.`,
      icon: '🚀'
    }
  ];

  const displaySections = sections || defaultSections;
  const displayCtaText = ctaText || 'Get Started';

  // Color scheme based on theme
  const colors = {
    background: theme.mode === 'dark' ? '#1f2937' : '#ffffff',
    headerBg: theme.mode === 'dark' ? '#111827' : '#f8fafc',
    border: theme.mode === 'dark' ? '#374151' : '#e2e8f0',
    text: theme.mode === 'dark' ? '#f3f4f6' : '#1e293b',
    textSecondary: theme.mode === 'dark' ? '#9ca3af' : '#64748b',
    accent: theme.accentColor || '#3b82f6',
    sectionBg: theme.mode === 'dark' ? '#374151' : '#f1f5f9'
  };

  return (
    <div
      className={`admesh-expandable-unit ${className}`}
      style={{
        fontFamily: theme.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        borderRadius: theme.borderRadius || '12px',
        border: `1px solid ${colors.border}`,
        backgroundColor: colors.background,
        overflow: 'hidden',
        maxWidth: '400px',
        boxShadow: theme.mode === 'dark' 
          ? '0 4px 6px -1px rgba(0, 0, 0, 0.3)' 
          : '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
      }}
      data-admesh-theme={theme.mode}
    >
      {/* Header */}
      <div
        style={{
          background: `linear-gradient(135deg, ${colors.headerBg} 0%, ${colors.accent}15 100%)`,
          padding: '16px',
          borderBottom: isExpanded || !collapsible ? `1px solid ${colors.border}` : 'none',
          position: 'relative'
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px', flex: 1, minWidth: 0 }}>
            <div
              style={{
                width: '40px',
                height: '40px',
                borderRadius: '8px',
                backgroundColor: colors.accent,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: '18px',
                fontWeight: 'bold'
              }}
            >
              {recommendation.title.charAt(0).toUpperCase()}
            </div>
            <div style={{ flex: 1, minWidth: 0 }}>
              <h3
                style={{
                  margin: 0,
                  fontSize: '18px',
                  fontWeight: '600',
                  color: colors.text,
                  lineHeight: '1.2'
                }}
              >
                {recommendation.title}
              </h3>
              <p
                style={{
                  margin: 0,
                  fontSize: '12px',
                  color: colors.textSecondary,
                  fontWeight: '500'
                }}
              >
                Sponsored • {new URL(recommendation.url || recommendation.admesh_link).hostname}
              </p>
            </div>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {/* CTA Button when collapsed */}
            {!isExpanded && collapsible && (
              <AdMeshLinkTracker
                adId={recommendation.ad_id}
                admeshLink={recommendation.admesh_link}
                productId={recommendation.product_id}
                onClick={handleClick}
                trackingData={{
                  title: recommendation.title,
                  component: 'expandable_unit',
                  expanded: false,
                  location: 'header'
                }}
              >
                <button
                  style={{
                    padding: '8px 16px',
                    backgroundColor: colors.accent,
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    fontSize: '13px',
                    fontWeight: '600',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                    whiteSpace: 'nowrap'
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.transform = 'translateY(-1px)';
                    e.currentTarget.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
                  }}
                >
                  {displayCtaText}
                </button>
              </AdMeshLinkTracker>
            )}

            {/* Expand/Collapse button */}
            {collapsible && (
              <button
                onClick={handleToggleExpand}
                style={{
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  padding: '4px',
                  borderRadius: '4px',
                  color: colors.textSecondary,
                  fontSize: '18px',
                  transition: 'transform 0.2s ease',
                  transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)'
                }}
              >
                ⌄
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Expandable Content */}
      {(isExpanded || !collapsible) && (
        <div style={{ padding: '0' }}>
          {/* Sections */}
          {displaySections.map((section, index) => (
            <div
              key={index}
              style={{
                padding: '16px',
                backgroundColor: index % 2 === 0 ? colors.background : colors.sectionBg,
                borderBottom: index < displaySections.length - 1 ? `1px solid ${colors.border}` : 'none'
              }}
            >
              <h4
                style={{
                  margin: '0 0 8px 0',
                  fontSize: '14px',
                  fontWeight: '600',
                  color: colors.text,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
              >
                {section.icon && <span>{section.icon}</span>}
                {section.title}
              </h4>
              <p
                style={{
                  margin: 0,
                  fontSize: '13px',
                  color: colors.textSecondary,
                  lineHeight: '1.4'
                }}
              >
                {section.description}
              </p>
            </div>
          ))}

          {/* CTA Button - only show when expanded or when not collapsible */}
          {(isExpanded || !collapsible) && (
            <div style={{ padding: '16px' }}>
              <AdMeshLinkTracker
                adId={recommendation.ad_id}
                admeshLink={recommendation.admesh_link}
                productId={recommendation.product_id}
                onClick={handleClick}
                trackingData={{
                  title: recommendation.title,
                  component: 'expandable_unit',
                  expanded: isExpanded,
                  location: 'footer'
                }}
              >
                <button
                  style={{
                    width: '100%',
                    padding: '12px 24px',
                    backgroundColor: colors.accent,
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontSize: '14px',
                    fontWeight: '600',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.transform = 'translateY(-1px)';
                    e.currentTarget.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
                  }}
                >
                  {displayCtaText}
                </button>
              </AdMeshLinkTracker>
            </div>
          )}

          {/* Powered by AdMesh */}
          {showPoweredBy && (
            <div
              style={{
                padding: '8px 16px',
                borderTop: `1px solid ${colors.border}`,
                backgroundColor: colors.headerBg
              }}
            >
              <div
                style={{
                  fontSize: '11px',
                  color: colors.textSecondary,
                  textAlign: 'center' as const
                }}
              >
                powered by <strong style={{ color: colors.text }}>AdMesh</strong>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AdMeshExpandableUnit;
